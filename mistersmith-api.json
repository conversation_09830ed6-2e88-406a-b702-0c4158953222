{"openapi": "3.0.3", "info": {"title": "MisterSmith Agent Orchestration API", "description": "Core API for MisterSmith multi-agent framework providing agent lifecycle management, task coordination, and NATS messaging integration", "version": "0.1.0", "contact": {"name": "MisterSmith Framework", "url": "https://github.com/MattMagg/Mister-<PERSON>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "http://localhost:8080", "description": "Local development server"}], "paths": {"/agents": {"get": {"summary": "List all agents", "description": "Retrieve a list of all agents in the system with their current status", "operationId": "listAgents", "tags": ["Agent Management"], "responses": {"200": {"description": "List of agents", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Agent"}}}}}}}, "post": {"summary": "Create a new agent", "description": "Spawn a new agent with specified configuration and capabilities", "operationId": "createAgent", "tags": ["Agent Management"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAgentRequest"}}}}, "responses": {"201": {"description": "Agent created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Agent"}}}}, "400": {"description": "Invalid agent configuration", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/agents/{agentId}": {"get": {"summary": "Get agent details", "description": "Retrieve detailed information about a specific agent", "operationId": "getAgent", "tags": ["Agent Management"], "parameters": [{"name": "agentId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "Unique identifier for the agent"}], "responses": {"200": {"description": "Agent details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Agent"}}}}, "404": {"description": "Agent not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "delete": {"summary": "Terminate agent", "description": "Gracefully terminate an agent and clean up its resources", "operationId": "terminateAgent", "tags": ["Agent Management"], "parameters": [{"name": "agentId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "Unique identifier for the agent"}], "responses": {"204": {"description": "Agent terminated successfully"}, "404": {"description": "Agent not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/tasks": {"post": {"summary": "Submit a task", "description": "Submit a new task to be executed by the agent orchestration system", "operationId": "submitTask", "tags": ["Task Management"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRequest"}}}}, "responses": {"202": {"description": "Task accepted for processing", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Task"}}}}, "400": {"description": "Invalid task request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/tasks/{taskId}": {"get": {"summary": "Get task status", "description": "Retrieve the current status and results of a task", "operationId": "getTask", "tags": ["Task Management"], "parameters": [{"name": "taskId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "Unique identifier for the task"}], "responses": {"200": {"description": "Task details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Task"}}}}, "404": {"description": "Task not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/system/health": {"get": {"summary": "System health check", "description": "Check the health status of the MisterSmith system and its components", "operationId": "getSystemHealth", "tags": ["System"], "responses": {"200": {"description": "System health status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthStatus"}}}}}}}, "/system/metrics": {"get": {"summary": "System metrics", "description": "Retrieve system performance metrics and statistics", "operationId": "getSystemMetrics", "tags": ["System"], "responses": {"200": {"description": "System metrics", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemMetrics"}}}}}}}}, "components": {"schemas": {"Agent": {"type": "object", "required": ["id", "name", "status", "created_at"], "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the agent"}, "name": {"type": "string", "description": "Human-readable name for the agent"}, "status": {"$ref": "#/components/schemas/AgentStatus"}, "capabilities": {"type": "array", "items": {"type": "string"}, "description": "List of capabilities this agent supports"}, "created_at": {"type": "string", "format": "date-time", "description": "When the agent was created"}, "last_activity": {"type": "string", "format": "date-time", "description": "Last time the agent was active"}, "metadata": {"type": "object", "additionalProperties": true, "description": "Additional agent metadata"}}}, "AgentStatus": {"type": "string", "enum": ["initializing", "idle", "busy", "error", "terminated"], "description": "Current status of the agent"}, "CreateAgentRequest": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Name for the new agent"}, "capabilities": {"type": "array", "items": {"type": "string"}, "description": "Capabilities to enable for this agent"}, "config": {"type": "object", "additionalProperties": true, "description": "Agent-specific configuration"}}}, "Task": {"type": "object", "required": ["id", "status", "created_at"], "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the task"}, "status": {"$ref": "#/components/schemas/TaskStatus"}, "description": {"type": "string", "description": "Human-readable description of the task"}, "assigned_agent": {"type": "string", "format": "uuid", "description": "ID of the agent assigned to this task"}, "created_at": {"type": "string", "format": "date-time", "description": "When the task was created"}, "completed_at": {"type": "string", "format": "date-time", "description": "When the task was completed"}, "result": {"type": "object", "additionalProperties": true, "description": "Task execution result"}, "error": {"type": "string", "description": "Error message if task failed"}}}, "TaskStatus": {"type": "string", "enum": ["pending", "assigned", "running", "completed", "failed", "cancelled"], "description": "Current status of the task"}, "TaskRequest": {"type": "object", "required": ["description"], "properties": {"description": {"type": "string", "description": "Description of the task to be performed"}, "priority": {"type": "integer", "minimum": 1, "maximum": 10, "default": 5, "description": "Task priority (1=lowest, 10=highest)"}, "required_capabilities": {"type": "array", "items": {"type": "string"}, "description": "Capabilities required to execute this task"}, "parameters": {"type": "object", "additionalProperties": true, "description": "Task-specific parameters"}}}, "HealthStatus": {"type": "object", "required": ["status", "timestamp"], "properties": {"status": {"type": "string", "enum": ["healthy", "degraded", "unhealthy"], "description": "Overall system health status"}, "timestamp": {"type": "string", "format": "date-time", "description": "When the health check was performed"}, "components": {"type": "object", "additionalProperties": {"type": "object", "properties": {"status": {"type": "string", "enum": ["healthy", "degraded", "unhealthy"]}, "message": {"type": "string"}}}, "description": "Health status of individual system components"}}}, "SystemMetrics": {"type": "object", "required": ["timestamp"], "properties": {"timestamp": {"type": "string", "format": "date-time", "description": "When the metrics were collected"}, "agent_count": {"type": "integer", "description": "Total number of active agents"}, "task_count": {"type": "integer", "description": "Total number of tasks in the system"}, "memory_usage": {"type": "number", "description": "Memory usage in MB"}, "cpu_usage": {"type": "number", "description": "CPU usage percentage"}, "nats_connections": {"type": "integer", "description": "Number of active NATS connections"}}}, "Error": {"type": "object", "required": ["error", "message"], "properties": {"error": {"type": "string", "description": "Error code"}, "message": {"type": "string", "description": "Human-readable error message"}, "details": {"type": "object", "additionalProperties": true, "description": "Additional error details"}}}}}, "tags": [{"name": "Agent Management", "description": "Operations for managing agent lifecycle"}, {"name": "Task Management", "description": "Operations for task submission and monitoring"}, {"name": "System", "description": "System health and monitoring operations"}]}