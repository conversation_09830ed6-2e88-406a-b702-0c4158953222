/Users/<USER>/Mister-<PERSON>/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/target/debug/libmistersmith_mcp_client.rlib: /Users/<USER>/Mister-<PERSON>/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/api/resource.rs /Users/<USER>/Mister-<PERSON>/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/application/auth.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/application/config.rs /Users/<USER>/Mister-<PERSON>/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/application/registry.rs /Users/<USER>/Mister-<PERSON>/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/application/result.rs /Users/<USER>/Mister-<PERSON>/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/application/session_manager.rs /Users/<USER>/Mister-<PERSON>/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/application/validation.rs /Users/<USER>/Mister-<PERSON>/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/application/validation_config.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/cli/args.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/cli/auth.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/cli/config.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/cli/headless.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/cli/interactive.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/cli/mod.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/cli/runner.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/domain/capabilities.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/domain/client.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/domain/connection.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/domain/operations.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/infrastructure/cache/resource_cache.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/infrastructure/database/manager.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/infrastructure/database/migrations.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/infrastructure/error.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/infrastructure/transport.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/lib.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/ui/headless.rs /Users/<USER>/Mister-Smith/MisterSmith/agenterra/mcp-foundation/mistersmith-mcp-client/src/ui/repl.rs
