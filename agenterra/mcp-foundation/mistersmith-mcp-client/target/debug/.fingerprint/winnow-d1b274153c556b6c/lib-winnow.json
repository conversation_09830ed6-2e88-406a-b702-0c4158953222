{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 18007145932999333080, "path": 14271653915872680617, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/winnow-d1b274153c556b6c/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}