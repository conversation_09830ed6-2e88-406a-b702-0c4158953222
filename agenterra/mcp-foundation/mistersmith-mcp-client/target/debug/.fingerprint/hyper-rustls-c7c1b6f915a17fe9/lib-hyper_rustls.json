{"rustc": 12610991425282158916, "features": "[\"http1\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 5347358027863023418, "path": 4909748749326187969, "deps": [[778154619793643451, "hyper_util", false, 6992101925028502695], [784494742817713399, "tower_service", false, 261318282831996449], [2883436298747778685, "pki_types", false, 9929363717612333779], [5907992341687085091, "webpki_roots", false, 6315339416989309172], [9010263965687315507, "http", false, 800334247552412508], [11895591994124935963, "tokio_rustls", false, 276708710514468102], [11957360342995674422, "hyper", false, 10306682306663405533], [12393800526703971956, "tokio", false, 8039932307206829044], [16400140949089969347, "rustls", false, 6354261513916154391]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hyper-rustls-c7c1b6f915a17fe9/dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}