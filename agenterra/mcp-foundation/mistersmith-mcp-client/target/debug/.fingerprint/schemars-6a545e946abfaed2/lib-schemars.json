{"rustc": 12610991425282158916, "features": "[\"chrono\", \"default\", \"derive\", \"schemars_derive\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 5347358027863023418, "path": 4375151095435409273, "deps": [[6913375703034175521, "build_script_build", false, 12519343328985773038], [9122563107207267705, "dyn_clone", false, 12289045770255438481], [9689903380558560274, "serde", false, 12757407212572919685], [9897246384292347999, "chrono", false, 14112890403786121294], [15367738274754116744, "serde_json", false, 5639188357941577567], [16071897500792579091, "schemars_derive", false, 6394743912567828132]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/schemars-6a545e946abfaed2/dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}