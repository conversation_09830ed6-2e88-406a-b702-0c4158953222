{"rustc": 12610991425282158916, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 5073168975548574389, "path": 11283821736962383835, "deps": [[2883436298747778685, "pki_types", false, 9929363717612333779], [3722963349756955755, "once_cell", false, 7535494358102297920], [5491919304041016563, "ring", false, 6332885578839218455], [6528079939221783635, "zeroize", false, 16164986982758783759], [16400140949089969347, "build_script_build", false, 17593398834110935826], [17003143334332120809, "subtle", false, 2958692638844484969], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 7836596212273075349]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustls-69b3fc8b821f903a/dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}