{"rustc": 12610991425282158916, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 5347358027863023418, "path": 7953703423384258125, "deps": [[784494742817713399, "tower_service", false, 261318282831996449], [1906322745568073236, "pin_project_lite", false, 7015933763304356685], [4121350475192885151, "iri_string", false, 2500255661313177199], [5695049318159433696, "tower", false, 6170885169010479546], [7712452662827335977, "tower_layer", false, 13514644392797441972], [7896293946984509699, "bitflags", false, 14868448029921109533], [9010263965687315507, "http", false, 800334247552412508], [10629569228670356391, "futures_util", false, 712174114433921550], [14084095096285906100, "http_body", false, 17422182689238069974], [16066129441945555748, "bytes", false, 17184268118052800933]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tower-http-b63c9eee84e663c4/dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}