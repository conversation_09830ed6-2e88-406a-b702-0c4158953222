{"rustc": 12610991425282158916, "features": "[\"bundled\", \"modern_sqlite\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"buildtime_bindgen\", \"bundled\", \"bundled-full\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"chrono\", \"collation\", \"column_decltype\", \"column_metadata\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"in_gecko\", \"jiff\", \"limits\", \"load_extension\", \"loadable_extension\", \"modern-full\", \"modern_sqlite\", \"preupdate_hook\", \"rusqlite-macros\", \"serde_json\", \"serialize\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"wasm32-wasi-vfs\", \"window\", \"with-asan\"]", "target": 10662205063260755052, "profile": 5347358027863023418, "path": 14985594447790750935, "deps": [[697300053061991528, "libsqlite3_sys", false, 7901768526668615542], [1303438375223863970, "hashlink", false, 9528625843301670805], [3666196340704888985, "smallvec", false, 12893359457295988057], [5510864063823219921, "fallible_streaming_iterator", false, 16717266159745487138], [7896293946984509699, "bitflags", false, 14868448029921109533], [12860549049674006569, "fallible_iterator", false, 6004881655039137055]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rusqlite-69857cc090b29c13/dep-lib-rusqlite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}