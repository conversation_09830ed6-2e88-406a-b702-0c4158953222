{"rustc": 12610991425282158916, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 8276155916380437441, "path": 7953703423384258125, "deps": [[784494742817713399, "tower_service", false, 10514186907451549013], [1906322745568073236, "pin_project_lite", false, 5920187675118069522], [4121350475192885151, "iri_string", false, 12720490841532375103], [5695049318159433696, "tower", false, 5493623026064533734], [7712452662827335977, "tower_layer", false, 15850285229297725815], [7896293946984509699, "bitflags", false, 1061054722497727190], [9010263965687315507, "http", false, 8994049454252157319], [10629569228670356391, "futures_util", false, 4085679859473920721], [14084095096285906100, "http_body", false, 13628876010885295326], [16066129441945555748, "bytes", false, 7724632493962288197]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tower-http-6dc7c21d222e5508/dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}