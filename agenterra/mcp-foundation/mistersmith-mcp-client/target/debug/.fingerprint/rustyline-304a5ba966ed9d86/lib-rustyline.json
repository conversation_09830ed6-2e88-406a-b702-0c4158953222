{"rustc": 12610991425282158916, "features": "[\"custom-bindings\", \"default\", \"fd-lock\", \"home\", \"radix_trie\", \"with-dirs\", \"with-file-history\"]", "declared_features": "[\"buffer-redux\", \"case_insensitive_history_search\", \"custom-bindings\", \"default\", \"derive\", \"fd-lock\", \"home\", \"radix_trie\", \"regex\", \"rusqlite\", \"rustyline-derive\", \"signal-hook\", \"skim\", \"termios\", \"with-dirs\", \"with-file-history\", \"with-fuzzy\", \"with-sqlite-history\"]", "target": 1801494506462427169, "profile": 5347358027863023418, "path": 18157283799029051479, "deps": [[1232198224951696867, "unicode_segmentation", false, 3827121568236944398], [2828590642173593838, "cfg_if", false, 15538402982096355174], [4544379658388519060, "home", false, 3830879624350140453], [4684437522915235464, "libc", false, 1601310136508477920], [5150833351789356492, "nix", false, 15625329165857897224], [5986029879202738730, "log", false, 721277480513231225], [7896293946984509699, "bitflags", false, 14868448029921109533], [9520952519707787197, "fd_lock", false, 16180294012404661311], [13495677209339419690, "radix_trie", false, 9802686948544106489], [13774335185398496026, "unicode_width", false, 5563718531207854773], [15932120279885307830, "memchr", false, 13656810103244077837], [17716308468579268865, "utf8parse", false, 3796749331537984031]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustyline-304a5ba966ed9d86/dep-lib-rustyline", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}