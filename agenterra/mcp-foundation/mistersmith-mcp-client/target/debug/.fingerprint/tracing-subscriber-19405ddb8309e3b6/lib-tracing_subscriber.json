{"rustc": 12610991425282158916, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 1006155289083248400, "path": 12903499937715301965, "deps": [[1009387600818341822, "matchers", false, 12746570450458514848], [1017461770342116999, "sharded_slab", false, 7741026630011553100], [1359731229228270592, "thread_local", false, 12326841006725989585], [3424551429995674438, "tracing_core", false, 10662583477154132253], [3666196340704888985, "smallvec", false, 12893359457295988057], [3722963349756955755, "once_cell", false, 7535494358102297920], [8606274917505247608, "tracing", false, 3846786392553175704], [8614575489689151157, "nu_ansi_term", false, 6478715695378762231], [9451456094439810778, "regex", false, 15944529603005917710], [10806489435541507125, "tracing_log", false, 13905159326853712612]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-19405ddb8309e3b6/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}