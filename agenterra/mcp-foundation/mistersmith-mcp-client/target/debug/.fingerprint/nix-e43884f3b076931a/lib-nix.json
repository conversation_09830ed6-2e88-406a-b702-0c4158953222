{"rustc": 12610991425282158916, "features": "[\"fs\", \"ioctl\", \"poll\", \"process\", \"signal\", \"term\"]", "declared_features": "[\"acct\", \"aio\", \"default\", \"dir\", \"env\", \"event\", \"fanotify\", \"feature\", \"fs\", \"hostname\", \"inotify\", \"ioctl\", \"kmod\", \"memoffset\", \"mman\", \"mount\", \"mqueue\", \"net\", \"personality\", \"pin-utils\", \"poll\", \"process\", \"pthread\", \"ptrace\", \"quota\", \"reboot\", \"resource\", \"sched\", \"signal\", \"socket\", \"syslog\", \"term\", \"time\", \"ucontext\", \"uio\", \"user\", \"zerocopy\"]", "target": 1600181213338542824, "profile": 5347358027863023418, "path": 11934137725220459936, "deps": [[2828590642173593838, "cfg_if", false, 15538402982096355174], [4684437522915235464, "libc", false, 1601310136508477920], [5150833351789356492, "build_script_build", false, 7093065587221909707], [7896293946984509699, "bitflags", false, 14868448029921109533]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/nix-e43884f3b076931a/dep-lib-nix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}