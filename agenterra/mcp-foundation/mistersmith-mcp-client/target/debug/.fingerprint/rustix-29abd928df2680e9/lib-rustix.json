{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 13113447432260090560, "path": 2519488933021587834, "deps": [[4684437522915235464, "libc", false, 1601310136508477920], [7896293946984509699, "bitflags", false, 14868448029921109533], [8253628577145923712, "libc_errno", false, 17799265673369290923], [12053020504183902936, "build_script_build", false, 10693310825958947351]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-29abd928df2680e9/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}