OUT_DIR: /Users/<USER>/Mister-<PERSON>/MisterSmith/agenterra/mistersmith-mcp-server/target/debug/build/utoipa-swagger-ui-3fc5a90ce7921769/out
SWAGGER_UI_DOWNLOAD_URL: https://github.com/swagger-api/swagger-ui/archive/refs/tags/v5.17.14.zip
cargo:rerun-if-env-changed=SWAGGER_UI_DOWNLOAD_URL
start download to : "/Users/<USER>/Mister-<PERSON>/MisterSmith/agenterra/mistersmith-mcp-server/target/debug/build/utoipa-swagger-ui-3fc5a90ce7921769/out/v5.17.14.zip"
reqwest feature: Err(NotPresent)
trying to download using `curl` system package
zip_top_level_folder: "swagger-ui-5.17.14"
SWAGGER_UI_OVERWRITE_FOLDER not found: "overwrite"
