{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"actix_extras\", \"auto_into_responses\", \"axum_extras\", \"chrono\", \"config\", \"debug\", \"decimal\", \"decimal_float\", \"indexmap\", \"jiff_0_2\", \"non_strict_integers\", \"rc_schema\", \"regex\", \"repr\", \"rocket_extras\", \"smallvec\", \"time\", \"ulid\", \"url\", \"uuid\", \"yaml\"]", "target": 7060440504916948280, "profile": 316250661675928978, "path": 2967575396077147788, "deps": [[3060637413840920116, "proc_macro2", false, 15226426097483048287], [4974441333307933176, "syn", false, 15675578404189486583], [17990358020177143287, "quote", false, 10587883592479038084]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/utoipa-gen-29e98cde9808b3cf/dep-lib-utoipa_gen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}