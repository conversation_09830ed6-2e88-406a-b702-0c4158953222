{"rustc": 12610991425282158916, "features": "[\"default\", \"macros\"]", "declared_features": "[\"actix_extras\", \"auto_into_responses\", \"axum_extras\", \"chrono\", \"config\", \"debug\", \"decimal\", \"decimal_float\", \"default\", \"indexmap\", \"jiff_0_2\", \"macros\", \"non_strict_integers\", \"openapi_extensions\", \"preserve_order\", \"preserve_path_order\", \"rc_schema\", \"repr\", \"rocket_extras\", \"serde_norway\", \"smallvec\", \"time\", \"ulid\", \"url\", \"uuid\", \"yaml\"]", "target": 13927031990295191028, "profile": 582315175729940664, "path": 14642439820379879587, "deps": [[6493259146304816786, "indexmap", false, 1064431216710423397], [7041231949368450205, "utoipa_gen", false, 13500242068793677562], [9689903380558560274, "serde", false, 84567774510934921], [15367738274754116744, "serde_json", false, 9192448082540761045]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/utoipa-547da20c25e78b67/dep-lib-utoipa", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}