{"rustc": 12610991425282158916, "features": "[\"builtins\", \"chrono\", \"chrono-tz\", \"default\", \"humansize\", \"percent-encoding\", \"rand\", \"slug\", \"urlencode\"]", "declared_features": "[\"builtins\", \"chrono\", \"chrono-tz\", \"date-locale\", \"default\", \"humansize\", \"percent-encoding\", \"preserve_order\", \"rand\", \"slug\", \"urlencode\"]", "target": 10510219667581518171, "profile": 8276155916380437441, "path": 8576631571177577337, "deps": [[40386456601120721, "percent_encoding", false, 8568388707745996276], [1142496670751353492, "unic_segment", false, 6575591513212182698], [2631894480810835227, "chrono_tz", false, 12082408892110239447], [3221585212778410572, "pest", false, 7771915210777366932], [6593674146359544692, "humansize", false, 6119827911767234893], [9451456094439810778, "regex", false, 15623309791309592786], [9689903380558560274, "serde", false, 84567774510934921], [9897246384292347999, "chrono", false, 800050305611171175], [9901698829223861929, "globwalk", false, 3066546418520968865], [12719040206398185542, "slug", false, 7681732981223992080], [13050370547047919637, "pest_derive", false, 8724011331686428887], [13208667028893622512, "rand", false, 12665612411778594235], [15367738274754116744, "serde_json", false, 9192448082540761045], [17917672826516349275, "lazy_static", false, 14734733467864199654]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tera-43a20855d6534885/dep-lib-tera", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}