{"rustc": 12610991425282158916, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 5129752230091461406, "path": 6249574996890450387, "deps": [[4925398738524877221, "clap_derive", false, 6996145484341581265], [14814905555676593471, "clap_builder", false, 5798000705384859613]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-9f868e89f6bfb22b/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}